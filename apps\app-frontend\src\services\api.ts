import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import {
  User,
  LoginResponse,
  TokenResponse,
  Invoice,
  ActionItem,
  TwoFASetupResponse,
  ApiError,
  AvailableIntegration,
  InvoiceIntegration,
  InvoiceIntegrationCreate,
  InvoiceIntegrationUpdate,
  IntegrationSyncResult,
  ManualSyncResponse,
  ScheduleSettings,
  ScheduleSettingsUpdate,
  ConnectionTestResult,
  SessionDetail,
  SessionsSummary,
  ProcessingTaskResponse,
  LLMProviderInfo,
  SessionStatus,
  ProcessingStep,
  Tenant,
  TenantUpdate
} from '../types';

const API_URL = process.env.REACT_APP_API_URL || window.location.origin;

// Create axios instance
const api = axios.create({
  baseURL: API_URL || undefined, // undefined means use same origin
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;

      // Add tenant header if available
      const tenantId = localStorage.getItem('current_tenant_id');
      if (tenantId) {
        config.headers['X-Tenant-ID'] = tenantId;
      }

      // Debug logging
      console.log('API Request:', {
        url: config.url,
        method: config.method,
        headers: {
          'Authorization': token ? 'Bearer [REDACTED]' : 'None',
          'X-Tenant-ID': tenantId || 'None'
        },
        fullHeaders: config.headers
      });
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError<ApiError>) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Debug logging for errors
    console.error('API Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }
        
        const response = await api.post<TokenResponse>('/api/v1/auth/refresh', {
          refresh_token: refreshToken
        });
        
        localStorage.setItem('access_token', response.data.access_token);
        localStorage.setItem('refresh_token', response.data.refresh_token);
        
        if (originalRequest.headers) {
          originalRequest.headers['Authorization'] = `Bearer ${response.data.access_token}`;
        }
        
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('current_tenant_id');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  login: async (email: string, password: string) => {
    const response = await api.post<LoginResponse>('/api/v1/auth/token', { email, password });
    return response.data;
  },

  verifyTwoFA: async (code: string, tempToken: string) => {
    const response = await api.post<TokenResponse>(
      '/api/v1/auth/2fa/verify',
      { code },
      { headers: { Authorization: `Bearer ${tempToken}` } }
    );
    return response.data;
  },
  
  getCurrentUser: async () => {
    const response = await api.get<User>('/api/v1/auth/me');
    return response.data;
  },

  setupTwoFA: async () => {
    const response = await api.post<TwoFASetupResponse>('/api/v1/auth/2fa/setup');
    return response.data;
  },

  enableTwoFA: async (code: string) => {
    const response = await api.post('/api/v1/auth/2fa/enable', { code });
    return response.data;
  },

  disableTwoFA: async (password: string, code: string) => {
    const response = await api.post('/api/v1/auth/2fa/disable', { password, code });
    return response.data;
  }
};

// Invoices API
export const invoicesApi = {
  getInvoices: async (params?: { status?: string, skip?: number, limit?: number }) => {
    const response = await api.get<Invoice[]>('/api/v1/invoices', { params });
    return response.data;
  },

  getInvoice: async (id: string) => {
    const response = await api.get<Invoice>(`/api/v1/invoices/${id}`);
    return response.data;
  },

  uploadInvoice: async (file: File, supplierName?: string) => {
    const formData = new FormData();
    formData.append('file', file);
    if (supplierName) {
      formData.append('supplier_name', supplierName);
    }

    const response = await api.post('/api/v1/invoices/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  validateInvoice: async (id: string) => {
    const response = await api.put(`/api/v1/invoices/${id}/validate`);
    return response.data;
  },

  deleteInvoice: async (id: string) => {
    const response = await api.delete(`/api/v1/invoices/${id}`);
    return response.data;
  },


};

// Action Items API
export const actionItemsApi = {
  getActionItems: async (params?: { completed?: boolean, priority?: string, category?: string }) => {
    const response = await api.get<ActionItem[]>('/api/v1/action-items', { params });
    return response.data;
  },

  getActionItem: async (id: string) => {
    const response = await api.get<ActionItem>(`/api/v1/action-items/${id}`);
    return response.data;
  },

  completeActionItem: async (id: string, resolutionNotes?: string) => {
    const response = await api.put(`/api/v1/action-items/${id}/complete`, { resolution_notes: resolutionNotes });
    return response.data;
  },

  reopenActionItem: async (id: string) => {
    const response = await api.put(`/api/v1/action-items/${id}/reopen`);
    return response.data;
  },

  deleteActionItem: async (id: string) => {
    const response = await api.delete(`/api/v1/action-items/${id}`);
    return response.data;
  }
};

// Users API
export const usersApi = {
  getUsers: async () => {
    const response = await api.get('/api/v1/users');
    return response.data;
  },

  getUser: async (id: string) => {
    const response = await api.get(`/api/v1/users/${id}`);
    return response.data;
  },

  updateUserRole: async (userId: string, roleId: string) => {
    const response = await api.put(`/api/v1/users/${userId}/role`, { role_id: roleId });
    return response.data;
  },

  deactivateUser: async (userId: string) => {
    const response = await api.put(`/api/v1/users/${userId}/deactivate`);
    return response.data;
  },

  activateUser: async (userId: string) => {
    const response = await api.put(`/api/v1/users/${userId}/activate`);
    return response.data;
  },

  getRoles: async () => {
    const response = await api.get('/api/v1/users/roles');
    return response.data;
  }
};

// Integrations API
export const integrationsApi = {
  getAvailableIntegrations: async () => {
    const response = await api.get<AvailableIntegration[]>('/api/v1/integrations/available');
    return response.data;
  },

  getIntegrations: async () => {
    const response = await api.get<InvoiceIntegration[]>('/api/v1/integrations');
    return response.data;
  },

  getIntegration: async (id: string) => {
    const response = await api.get<InvoiceIntegration>(`/api/v1/integrations/${id}`);
    return response.data;
  },

  createIntegration: async (data: InvoiceIntegrationCreate) => {
    const response = await api.post<InvoiceIntegration>('/api/v1/integrations/setup', data);
    return response.data;
  },

  updateIntegration: async (id: string, data: InvoiceIntegrationUpdate) => {
    const response = await api.put<InvoiceIntegration>(`/api/v1/integrations/${id}`, data);
    return response.data;
  },

  deleteIntegration: async (id: string) => {
    const response = await api.delete(`/api/v1/integrations/${id}`);
    return response.data;
  },

  testConnection: async (id: string) => {
    const response = await api.post<ConnectionTestResult>(`/api/v1/integrations/${id}/test-connection`);
    return response.data;
  },

  syncIntegration: async (id: string) => {
    const response = await api.post<IntegrationSyncResult>(`/api/v1/integrations/${id}/sync`);
    return response.data;
  },

  syncAllIntegrations: async () => {
    const response = await api.post<ManualSyncResponse>('/api/v1/integrations/sync-all');
    return response.data;
  },

  getScheduleSettings: async () => {
    const response = await api.get<ScheduleSettings>('/api/v1/integrations/schedule');
    return response.data;
  },

  updateScheduleSettings: async (data: ScheduleSettingsUpdate) => {
    const response = await api.put<ScheduleSettings>('/api/v1/integrations/schedule', data);
    return response.data;
  }
};

// Invoice Processing API
export const invoiceProcessingApi = {
  createInvoice: async (formData: FormData) => {
    // Convert file to base64
    const file = formData.get('file') as File;

    const fileBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(fileBuffer);
    let binary = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    const base64 = btoa(binary);

    const requestData = {
      import_typ: 'manuell',
      file_data: base64,
      metadata_ERP: null,
      original_filename: file.name,
      file_type: file.type.split('/')[1]
    };

    const response = await api.post<ProcessingTaskResponse>('/api/v1/invoice-processing/invoices', requestData);
    return response.data;
  },

  getSessions: async (params?: {
    limit?: number;
    offset?: number;
    status_filter?: SessionStatus
  }) => {
    const response = await api.get<SessionsSummary>('/api/v1/invoice-processing/sessions', { params });
    return response.data;
  },

  getSessionDetail: async (sessionId: string) => {
    const response = await api.get<SessionDetail>(`/api/v1/invoice-processing/sessions/${sessionId}`);
    return response.data;
  },

  retrySession: async (sessionId: string, fromStep?: ProcessingStep) => {
    const response = await api.post<ProcessingTaskResponse>(
      `/api/v1/invoice-processing/sessions/${sessionId}/retry`,
      { from_step: fromStep }
    );
    return response.data;
  },

  executeStep: async (sessionId: string, stepName: ProcessingStep) => {
    const response = await api.post<ProcessingTaskResponse>(
      `/api/v1/invoice-processing/sessions/${sessionId}/execute-step`,
      { step_name: stepName }
    );
    return response.data;
  },

  deleteSession: async (sessionId: string) => {
    const response = await api.delete(`/api/v1/invoice-processing/sessions/${sessionId}`);
    return response.data;
  },

  getLLMProviderInfo: async () => {
    const response = await api.get<LLMProviderInfo>('/api/v1/invoice-processing/llm-provider');
    return response.data;
  },

  getAvailablePrompts: async () => {
    const response = await api.get<{ prompts: Record<string, string> }>('/api/v1/invoice-processing/prompts');
    return response.data;
  }
};

// Tenant API
export const tenantApi = {
  getTenant: async () => {
    const response = await api.get<Tenant>('/api/v1/tenant');
    return response.data;
  },

  updateTenant: async (data: TenantUpdate) => {
    const response = await api.put<Tenant>('/api/v1/tenant', data);
    return response.data;
  }
};


